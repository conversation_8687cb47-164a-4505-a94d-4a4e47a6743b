﻿using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Attributes;
using Jint;
using Jint.Native.Json;
using System.Collections;
using System.Linq;

namespace GCP.Functions.Common.ScriptExtensions
{
    public class JavascriptUtils
    {
        private DbContext db { get; set; }

        public JavascriptUtils(DbContext db = null)
        {
            this.db = db;
        }

        [UtilsFunction("随机ID", "生成随机的UUID字符串", "Common", "常用函数", "string")]
        [FunctionExample("基本用法", "Utils.UUID()", "\"abc123def456\"", "生成一个随机的UUID字符串")]
        [FunctionExample("在变量中使用", "var id = Utils.UUID(); console.log(id);", "\"abc123def456\"", "将生成的UUID保存到变量中")]
        public string UUID() => TUID.NewTUID().ToString();

        [UtilsFunction("当前时间", "获取当前日期和时间", "Common", "常用函数", "DateTime")]
        [FunctionExample("基本用法", "Utils.NOW()", "2024-01-15 14:30:25", "获取当前的日期和时间")]
        [FunctionExample("格式化当前时间", "Utils.DATE_TO_STRING(Utils.NOW(), \"yyyy-MM-dd\")", "\"2024-01-15\"", "获取当前日期并格式化为字符串")]
        public DateTime NOW() => DateTime.Now;

        [UtilsFunction("条件判断", "根据条件返回不同的值", "Common", "常用函数", "object")]
        [FunctionExample("基本条件判断", "Utils.IF(true, \"是\", \"否\")", "\"是\"", "当条件为真时返回第一个值")]
        [FunctionExample("数值比较", "Utils.IF(age >= 18, \"成年人\", \"未成年人\")", "\"成年人\"", "根据年龄判断是否成年")]
        [FunctionExample("嵌套使用", "Utils.IF(score >= 90, \"优秀\", Utils.IF(score >= 60, \"及格\", \"不及格\"))", "\"优秀\"", "多层条件判断")]
        public object IF([ParameterDescription("条件表达式")] bool condition,
                        [ParameterDescription("条件为真时的值")] object trueValue,
                        [ParameterDescription("条件为假时的值")] object falseValue)
        {
            return condition ? trueValue : falseValue;
        }

        [UtilsFunction("时间转文本", "将时间转换为指定格式的字符串", "Common", "常用函数", "string")]
        [FunctionExample("默认格式", "Utils.DATE_TO_STRING(Utils.NOW())", "\"2024-01-15 14:30:25\"", "使用默认格式转换时间")]
        [FunctionExample("日期格式", "Utils.DATE_TO_STRING(Utils.NOW(), \"yyyy-MM-dd\")", "\"2024-01-15\"", "只显示日期部分")]
        [FunctionExample("时间格式", "Utils.DATE_TO_STRING(Utils.NOW(), \"HH:mm:ss\")", "\"14:30:25\"", "只显示时间部分")]
        [FunctionExample("中文格式", "Utils.DATE_TO_STRING(Utils.NOW(), \"yyyy年MM月dd日\")", "\"2024年01月15日\"", "使用中文日期格式")]
        public object DATE_TO_STRING([ParameterDescription("要转换的时间")] DateTime time,
                                    [ParameterDescription("时间格式")] string format = "yyyy-MM-dd HH:mm:ss")
        {
            return time.ToLocalTime().ToString(format);
        }

        [UtilsFunction("数据库查询", "执行SQL查询并返回第一条结果", "Common", "常用函数", "object")]
        public object DB_FIRST([ParameterDescription("SQL查询语句")] string sqlString,
                              [ParameterDescription("查询参数")] params object[] args)
        {
            return db.Sql(sqlString, args).Get();
        }

        private static readonly Engine _engine;

        static JavascriptUtils()
        {
            _engine = new Engine();
            _engine.Execute("""

                            function groupByMultipleFieldsDynamic(array, groupFields, fields, childField = 'items') {
                              if (!array || array.length === 0) return [];
                              if (!fields || fields.length === 0) {
                                fields = Object.keys(array[0]).filter((field) => !groupFields.includes(field));
                              }

                              return array.reduce((result, item) => {
                                const group = result.find((g) => groupFields.every((field) => item[field] === g[field]));

                                const extra = {};
                                fields.forEach((field) => {
                                  extra[field] = item[field];
                                });

                                if (group) {
                                  group[childField].push(extra);
                                } else {
                                  const dataItem = { [childField]: [] };
                                  groupFields.forEach((field) => {
                                    dataItem[field] = item[field];
                                  });
                                  dataItem[childField].push(extra);

                                  result.push(dataItem);
                                }
                                return result;
                              }, []);
                            }

                            """);
            _engine.Execute("""

                            function jsonParse(jsonString) {
                              return JSON.parse(jsonString);
                            }

                            """);
        }

        [UtilsFunction("动态字段分组", "根据动态字段对数组进行分组", "Array", "数组操作", "array")]
        public object GROUP_BY_DYNAMIC_FIELD([ParameterDescription("要分组的数组")] object array,
                                           [ParameterDescription("分组字段数组")] string[] groupFields,
                                           [ParameterDescription("其他字段数组")] string[] fields,
                                           [ParameterDescription("子节点字段名")] string childField = "body")
        {
            return _engine.Invoke("groupByMultipleFieldsDynamic", array, groupFields, fields, childField);
        }

        [UtilsFunction("JSON解析", "解析JSON字符串为对象", "Common", "常用函数", "object")]
        [FunctionExample("解析对象", "Utils.JSON_PARSE('{\"name\":\"张三\",\"age\":25}')", "{name: \"张三\", age: 25}", "解析JSON字符串为对象")]
        [FunctionExample("解析数组", "Utils.JSON_PARSE('[1,2,3,4,5]')", "[1, 2, 3, 4, 5]", "解析JSON数组")]
        [FunctionExample("访问属性", "Utils.JSON_PARSE('{\"user\":{\"name\":\"李四\"}}').user.name", "\"李四\"", "解析后访问嵌套属性")]
        public object JSON_PARSE([ParameterDescription("JSON字符串")] string jsonString)
        {
            var parser = new JsonParser(_engine);
            return parser.Parse(jsonString).ToObject();
            //return (_engine.Invoke("jsonParse", jsonString) as JsObject).ToObject();
        }

        #region 字符串处理函数
        /// <summary>
        /// 字符串拼接
        /// </summary>
        [UtilsFunction("字符串拼接", "将多个值拼接成字符串", "String", "字符串处理", "string")]
        [FunctionExample("基本拼接", "Utils.STRING_CONCAT(\"Hello\", \" \", \"World\")", "\"Hello World\"", "拼接多个字符串")]
        [FunctionExample("数字拼接", "Utils.STRING_CONCAT(\"订单号:\", 12345)", "\"订单号:12345\"", "拼接字符串和数字")]
        [FunctionExample("变量拼接", "Utils.STRING_CONCAT(name, \"的年龄是\", age, \"岁\")", "\"张三的年龄是25岁\"", "拼接变量和文本")]
        public string STRING_CONCAT([ParameterDescription("要拼接的值数组")] params object[] values)
        {
            return string.Concat(values?.Select(v => v?.ToString() ?? "") ?? new string[0]);
        }

        /// <summary>
        /// 字符串截取
        /// </summary>
        [UtilsFunction("字符串截取", "截取字符串的指定部分", "String", "字符串处理", "string")]
        public string STRING_SUBSTRING([ParameterDescription("输入字符串")] string input,
                                     [ParameterDescription("开始位置")] int startIndex,
                                     [ParameterDescription("截取长度")] int? length = null)
        {
            if (string.IsNullOrEmpty(input) || startIndex < 0 || startIndex >= input.Length)
                return "";

            if (length.HasValue)
            {
                var actualLength = Math.Min(length.Value, input.Length - startIndex);
                return actualLength > 0 ? input.Substring(startIndex, actualLength) : "";
            }
            return input.Substring(startIndex);
        }

        /// <summary>
        /// 字符串替换
        /// </summary>
        [UtilsFunction("字符串替换", "替换字符串中的指定内容", "String", "字符串处理")]
        public string STRING_REPLACE(string input, string oldValue, string newValue)
        {
            return input?.Replace(oldValue ?? "", newValue ?? "") ?? "";
        }

        /// <summary>
        /// 去除空格
        /// </summary>
        [UtilsFunction("去除空格", "去除字符串首尾空格", "String", "字符串处理")]
        public string STRING_TRIM(string input)
        {
            return input?.Trim() ?? "";
        }

        /// <summary>
        /// 转换为大写
        /// </summary>
        [UtilsFunction("转大写", "将字符串转换为大写", "String", "字符串处理")]
        public string STRING_UPPER(string input)
        {
            return input?.ToUpper() ?? "";
        }

        /// <summary>
        /// 转换为小写
        /// </summary>
        [UtilsFunction("转小写", "将字符串转换为小写", "String", "字符串处理")]
        public string STRING_LOWER(string input)
        {
            return input?.ToLower() ?? "";
        }

        /// <summary>
        /// 字符串分割
        /// </summary>
        [UtilsFunction("字符串分割", "按分隔符分割字符串", "String", "字符串处理")]
        public string[] STRING_SPLIT(string input, string separator)
        {
            if (string.IsNullOrEmpty(input)) return new string[0];
            return input.Split(new[] { separator }, StringSplitOptions.None);
        }

        /// <summary>
        /// 字符串长度
        /// </summary>
        [UtilsFunction("字符串长度", "获取字符串长度", "String", "字符串处理")]
        public int STRING_LENGTH(string input)
        {
            return input?.Length ?? 0;
        }

        /// <summary>
        /// 字符串是否包含
        /// </summary>
        [UtilsFunction("包含检查", "检查字符串是否包含指定内容", "String", "字符串处理")]
        public bool STRING_CONTAINS(string input, string value)
        {
            return input?.Contains(value ?? "") ?? false;
        }

        /// <summary>
        /// 字符串开始于
        /// </summary>
        [UtilsFunction("开始检查", "检查字符串是否以指定内容开始", "String", "字符串处理")]
        public bool STRING_STARTS_WITH(string input, string value)
        {
            return input?.StartsWith(value ?? "") ?? false;
        }

        /// <summary>
        /// 字符串结束于
        /// </summary>
        [UtilsFunction("结束检查", "检查字符串是否以指定内容结束", "String", "字符串处理")]
        public bool STRING_ENDS_WITH(string input, string value)
        {
            return input?.EndsWith(value ?? "") ?? false;
        }
        #endregion

        #region 数组处理函数
        /// <summary>
        /// 数组长度
        /// </summary>
        [UtilsFunction("数组长度", "获取数组或集合的长度", "Array", "数组操作", "number")]
        [FunctionExample("基本用法", "Utils.ARRAY_LENGTH([1, 2, 3, 4, 5])", "5", "获取数组的长度")]
        [FunctionExample("空数组", "Utils.ARRAY_LENGTH([])", "0", "空数组的长度为0")]
        [FunctionExample("字符串数组", "Utils.ARRAY_LENGTH([\"a\", \"b\", \"c\"])", "3", "获取字符串数组的长度")]
        public int ARRAY_LENGTH([ParameterDescription("输入数组")] object array)
        {
            if (array is Array arr) return arr.Length;
            if (array is System.Collections.ICollection collection) return collection.Count;
            return 0;
        }

        /// <summary>
        /// 数组是否包含元素
        /// </summary>
        [UtilsFunction("包含检查", "检查数组是否包含指定元素", "Array", "数组操作", "boolean")]
        [FunctionExample("数字检查", "Utils.ARRAY_CONTAINS([1, 2, 3, 4, 5], 3)", "true", "检查数组是否包含数字3")]
        [FunctionExample("字符串检查", "Utils.ARRAY_CONTAINS([\"apple\", \"banana\", \"orange\"], \"banana\")", "true", "检查数组是否包含指定字符串")]
        [FunctionExample("不存在的元素", "Utils.ARRAY_CONTAINS([1, 2, 3], 5)", "false", "检查不存在的元素返回false")]
        public bool ARRAY_CONTAINS([ParameterDescription("输入数组")] object array,
                                  [ParameterDescription("要检查的值")] object value)
        {
            if (array is Array arr)
            {
                return arr.Cast<object>().Contains(value);
            }
            if (array is System.Collections.IEnumerable enumerable)
            {
                return enumerable.Cast<object>().Contains(value);
            }
            return false;
        }

        /// <summary>
        /// 获取数组第一个元素
        /// </summary>
        [UtilsFunction("第一个元素", "获取数组第一个元素", "Array", "数组操作")]
        public object ARRAY_FIRST(object array)
        {
            if (array is Array arr && arr.Length > 0) return arr.GetValue(0);
            if (array is System.Collections.IEnumerable enumerable)
            {
                return enumerable.Cast<object>().FirstOrDefault();
            }
            return null;
        }

        /// <summary>
        /// 获取数组最后一个元素
        /// </summary>
        [UtilsFunction("最后一个元素", "获取数组最后一个元素", "Array", "数组操作")]
        public object ARRAY_LAST(object array)
        {
            if (array is Array arr && arr.Length > 0) return arr.GetValue(arr.Length - 1);
            if (array is System.Collections.IEnumerable enumerable)
            {
                return enumerable.Cast<object>().LastOrDefault();
            }
            return null;
        }

        /// <summary>
        /// 数组连接为字符串
        /// </summary>
        [UtilsFunction("数组连接", "将数组元素连接为字符串", "Array", "数组操作")]
        public string ARRAY_JOIN(object array, string separator = ",")
        {
            if (array is Array arr)
            {
                return string.Join(separator, arr.Cast<object>().Select(x => x?.ToString() ?? ""));
            }
            if (array is System.Collections.IEnumerable enumerable)
            {
                return string.Join(separator, enumerable.Cast<object>().Select(x => x?.ToString() ?? ""));
            }
            return "";
        }
        #endregion

        #region 时间处理函数
        /// <summary>
        /// 日期加减
        /// </summary>
        [UtilsFunction("日期加减", "对日期进行加减运算", "DateTime", "日期时间")]
        public DateTime DATE_ADD(DateTime date, int value, string unit = "days")
        {
            return unit.ToLower() switch
            {
                "years" => date.AddYears(value),
                "months" => date.AddMonths(value),
                "days" => date.AddDays(value),
                "hours" => date.AddHours(value),
                "minutes" => date.AddMinutes(value),
                "seconds" => date.AddSeconds(value),
                _ => date.AddDays(value)
            };
        }

        /// <summary>
        /// 日期差值
        /// </summary>
        [UtilsFunction("日期差值", "计算两个日期的差值", "DateTime", "日期时间")]
        public double DATE_DIFF(DateTime date1, DateTime date2, string unit = "days")
        {
            var timeSpan = date1 - date2;
            return unit.ToLower() switch
            {
                "years" => timeSpan.TotalDays / 365.25,
                "months" => timeSpan.TotalDays / 30.44,
                "days" => timeSpan.TotalDays,
                "hours" => timeSpan.TotalHours,
                "minutes" => timeSpan.TotalMinutes,
                "seconds" => timeSpan.TotalSeconds,
                _ => timeSpan.TotalDays
            };
        }

        /// <summary>
        /// 日期格式化
        /// </summary>
        [UtilsFunction("日期格式化", "格式化日期为字符串", "DateTime", "日期时间")]
        public string DATE_FORMAT(DateTime date, string format = "yyyy-MM-dd HH:mm:ss")
        {
            return date.ToString(format);
        }

        /// <summary>
        /// 日期解析
        /// </summary>
        [UtilsFunction("日期解析", "解析字符串为日期", "DateTime", "日期时间")]
        public DateTime? DATE_PARSE(string dateString)
        {
            if (DateTime.TryParse(dateString, out var result))
                return result;
            return null;
        }

        /// <summary>
        /// 获取当前时间戳
        /// </summary>
        [UtilsFunction("时间戳", "获取日期的时间戳", "DateTime", "日期时间")]
        public long DATE_TIMESTAMP(DateTime? date = null)
        {
            var targetDate = date ?? DateTime.Now;
            return ((DateTimeOffset)targetDate).ToUnixTimeSeconds();
        }

        /// <summary>
        /// 从时间戳获取日期
        /// </summary>
        [UtilsFunction("时间戳转日期", "从时间戳获取日期", "DateTime", "日期时间")]
        public DateTime DATE_FROM_TIMESTAMP(long timestamp)
        {
            return DateTimeOffset.FromUnixTimeSeconds(timestamp).DateTime;
        }
        #endregion

        #region 字典操作函数
        /// <summary>
        /// 获取字典值
        /// </summary>
        [UtilsFunction("获取字典值", "从字典中获取指定键的值", "Dictionary", "字典操作")]
        public object DICT_GET(object dict, string key, object defaultValue = null)
        {
            if (dict is IDictionary<string, object> dictionary)
            {
                return dictionary.TryGetValue(key, out var value) ? value : defaultValue;
            }
            if (dict is System.Collections.IDictionary iDict)
            {
                return iDict.Contains(key) ? iDict[key] : defaultValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// 设置字典值
        /// </summary>
        [UtilsFunction("设置字典值", "设置字典的键值对", "Dictionary", "字典操作")]
        public Dictionary<string, object> DICT_SET(object dict, string key, object value)
        {
            Dictionary<string, object> result;
            if (dict is Dictionary<string, object> dictionary)
            {
                result = new Dictionary<string, object>(dictionary);
            }
            else if (dict is IDictionary<string, object> iDict)
            {
                result = new Dictionary<string, object>(iDict);
            }
            else
            {
                result = new Dictionary<string, object>();
            }

            result[key] = value;
            return result;
        }

        /// <summary>
        /// 字典合并
        /// </summary>
        [UtilsFunction("字典合并", "合并多个字典", "Dictionary", "字典操作")]
        public Dictionary<string, object> DICT_MERGE(params object[] dicts)
        {
            var result = new Dictionary<string, object>();
            foreach (var dict in dicts)
            {
                if (dict is IDictionary<string, object> dictionary)
                {
                    foreach (var kvp in dictionary)
                    {
                        result[kvp.Key] = kvp.Value;
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 获取字典所有键
        /// </summary>
        [UtilsFunction("获取所有键", "获取字典的所有键", "Dictionary", "字典操作")]
        public string[] DICT_KEYS(object dict)
        {
            if (dict is IDictionary<string, object> dictionary)
            {
                return dictionary.Keys.ToArray();
            }
            if (dict is System.Collections.IDictionary iDict)
            {
                return iDict.Keys.Cast<string>().ToArray();
            }
            return new string[0];
        }

        /// <summary>
        /// 获取字典所有值
        /// </summary>
        [UtilsFunction("获取所有值", "获取字典的所有值", "Dictionary", "字典操作")]
        public object[] DICT_VALUES(object dict)
        {
            if (dict is IDictionary<string, object> dictionary)
            {
                return dictionary.Values.ToArray();
            }
            if (dict is System.Collections.IDictionary iDict)
            {
                return iDict.Values.Cast<object>().ToArray();
            }
            return new object[0];
        }

        /// <summary>
        /// 检查字典是否包含键
        /// </summary>
        [UtilsFunction("检查键存在", "检查字典是否包含指定键", "Dictionary", "字典操作")]
        public bool DICT_HAS_KEY(object dict, string key)
        {
            if (dict is IDictionary<string, object> dictionary)
            {
                return dictionary.ContainsKey(key);
            }
            if (dict is System.Collections.IDictionary iDict)
            {
                return iDict.Contains(key);
            }
            return false;
        }
        #endregion

        #region 逻辑和数学函数

        /// <summary>
        /// 数值比较
        /// </summary>
        [UtilsFunction("数值比较", "比较两个数值", "Math", "数学运算")]
        public bool COMPARE(object value1, string operator_, object value2)
        {
            try
            {
                var num1 = Convert.ToDouble(value1);
                var num2 = Convert.ToDouble(value2);

                return operator_ switch
                {
                    ">" => num1 > num2,
                    ">=" => num1 >= num2,
                    "<" => num1 < num2,
                    "<=" => num1 <= num2,
                    "==" => Math.Abs(num1 - num2) < double.Epsilon,
                    "!=" => Math.Abs(num1 - num2) >= double.Epsilon,
                    _ => false
                };
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 数学运算
        /// </summary>
        [UtilsFunction("数学计算", "执行基本的数学运算", "Math", "数学运算")]
        public double MATH_CALC(double value1, string operator_, double value2)
        {
            return operator_ switch
            {
                "+" => value1 + value2,
                "-" => value1 - value2,
                "*" => value1 * value2,
                "/" => value2 != 0 ? value1 / value2 : 0,
                "%" => value2 != 0 ? value1 % value2 : 0,
                "^" => Math.Pow(value1, value2),
                _ => 0
            };
        }

        /// <summary>
        /// 数值四舍五入
        /// </summary>
        [UtilsFunction("四舍五入", "数值四舍五入到指定小数位", "Math", "数学运算")]
        public double MATH_ROUND(double value, int digits = 0)
        {
            return Math.Round(value, digits);
        }

        /// <summary>
        /// 数值向上取整
        /// </summary>
        [UtilsFunction("向上取整", "数值向上取整", "Math", "数学运算")]
        public double MATH_CEIL(double value)
        {
            return Math.Ceiling(value);
        }

        /// <summary>
        /// 数值向下取整
        /// </summary>
        [UtilsFunction("向下取整", "数值向下取整", "Math", "数学运算")]
        public double MATH_FLOOR(double value)
        {
            return Math.Floor(value);
        }

        /// <summary>
        /// 绝对值
        /// </summary>
        [UtilsFunction("绝对值", "获取数值的绝对值", "Math", "数学运算")]
        public double MATH_ABS(double value)
        {
            return Math.Abs(value);
        }
        #endregion
    }
}
