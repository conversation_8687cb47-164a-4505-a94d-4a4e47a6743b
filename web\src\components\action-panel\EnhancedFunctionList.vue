<template>
  <div class="enhanced-function-list">
    <div class="function-list-container">
      <!-- 左侧：函数分类树 -->
      <div class="function-tree-panel">
        <div class="panel-header">
          <h4>函数分类</h4>
          <t-input v-model="searchKeyword" placeholder="搜索函数..." size="small" clearable>
            <template #prefix-icon>
              <search-icon />
            </template>
          </t-input>
        </div>
        <div class="tree-container">
          <t-tree
            :data="filteredData"
            expand-all
            activable
            transition
            expand-on-click-node
            hover
            @click="onFunctionClick"
            @dblclick="onFunctionDblClick"
          >
            <template #label="{ node }">
              <div
                class="tree-node-label"
                @click="onNodeLabelClick(node.data || node)"
                @dblclick="onNodeLabelDblClick(node.data || node, $event)"
              >
                <span v-html="highlightSearchKeyword(node.label)"></span>
                <t-tag v-if="(node.data || node).script" size="small" theme="primary" variant="outline">
                  {{ getFunctionCategory((node.data || node).value) }}
                </t-tag>
              </div>
            </template>
          </t-tree>
        </div>
      </div>

      <!-- 右侧：函数详情 -->
      <div class="function-detail-panel">
        <div v-if="activeFunction" class="function-detail">
          <!-- 函数头部信息 -->
          <div class="function-header">
            <div class="function-title-section">
              <h2 class="function-title">{{ activeFunction.label }}</h2>
              <t-space>
                <t-tag theme="primary" size="medium">{{ getFunctionCategory(activeFunction.value) }}</t-tag>
                <t-button size="small" variant="outline" @click="copyToClipboard(activeFunction.script)">
                  <template #icon>
                    <copy-icon />
                  </template>
                  复制代码
                </t-button>
                <t-button
                  size="small"
                  theme="primary"
                  :disabled="!activeFunction"
                  @click="selectFunction(activeFunction)"
                >
                  <template #icon>
                    <check-icon />
                  </template>
                  选择此函数
                </t-button>
              </t-space>
            </div>
          </div>

          <!-- 函数使用方法 -->
          <div class="function-section">
            <h3>使用方法</h3>
            <div class="code-preview-container">
              <code-preview :code="activeFunction.script" lang="javascript" />
            </div>
          </div>

          <!-- 函数说明 -->
          <div class="function-section">
            <h3>功能说明</h3>
            <div class="function-description">
              <p>{{ activeFunction.remark }}</p>
            </div>
          </div>

          <!-- 参数说明 -->
          <div v-if="functionParameters.length > 0" class="function-section">
            <h3>参数说明</h3>
            <t-table :data="functionParameters" :columns="paramColumns" size="small" bordered />
          </div>

          <!-- 使用示例 -->
          <div class="function-section">
            <h3>使用示例</h3>
            <div class="examples-container">
              <div v-for="(example, index) in functionExamples" :key="index" class="example-item">
                <div class="example-header">
                  <h4>{{ example.title }}</h4>
                </div>
                <div class="example-code">
                  <code-preview :code="example.code" lang="javascript" />
                </div>
                <div v-if="example.result" class="example-result">
                  <span class="result-label">预期结果:</span>
                  <code class="result-code">{{ example.result }}</code>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 未选择函数时的提示 -->
        <div v-else class="no-selection">
          <div class="no-selection-content">
            <t-icon name="function" size="64px" style="color: var(--td-text-color-placeholder)" />
            <h3>选择一个函数查看详细说明</h3>
            <p>从左侧列表中选择函数，查看使用方法、参数说明和示例代码</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'EnhancedFunctionList',
};
</script>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { SearchIcon, CopyIcon, CheckIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import CodePreview from '@/components/code-preview/index.vue';
import { VALUE_TYPE_MAP } from './constants';
import { getFunctionCategories, type FunctionCategory } from '@/composables/services/functionDataService';

// Props (暂时未使用)
// const props = defineProps<{
//   filterText?: string;
// }>();

// Emits
const emits = defineEmits<{
  dblclick: [func: any];
}>();

// 响应式数据
const searchKeyword = ref('');
const activeFunction = ref<any>(null);
const loading = ref(false);

// 函数数据
const functionData = ref<FunctionCategory[]>([]);

// 加载函数数据
const loadFunctionData = async () => {
  loading.value = true;
  try {
    const data = await getFunctionCategories();
    functionData.value = data || [];
    console.log(`EnhancedFunctionList: 已加载 ${data.length} 个函数分类`);
  } catch (error) {
    console.error('加载函数数据失败:', error);
    MessagePlugin.error('加载函数数据失败');
    functionData.value = [];
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadFunctionData();
});

// 计算属性
const filteredData = computed(() => {
  if (!searchKeyword.value) {
    return functionData.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return functionData.value
    .map((category: any) => ({
      ...category,
      children: (category.children || []).filter(
        (func: any) => func.label?.toLowerCase().includes(keyword) || func.value?.toLowerCase().includes(keyword),
      ),
    }))
    .filter((category: any) => category.children && category.children.length > 0);
});

const functionParameters = computed(() => {
  if (!activeFunction.value?.parameters) return [];
  return activeFunction.value.parameters;
});

const functionExamples = computed(() => {
  if (!activeFunction.value?.examples) return [];
  return activeFunction.value.examples;
});

// 表格列定义
const paramColumns = [
  { colKey: 'name', title: '参数名', width: 120 },
  { colKey: 'type', title: '类型', width: 100 },
  { colKey: 'required', title: '必需', width: 80, cell: (_: any, { row }: any) => (row.required ? '是' : '否') },
  { colKey: 'description', title: '说明', ellipsis: true },
];

// 方法
const onFunctionClick = (context: any) => {
  // TDesign Tree 组件的数据结构
  let nodeData = null;

  // 检查是否有 node 属性（TDesign Tree 的标准结构）
  if (context && context.node) {
    // TDesign Tree 的节点数据通常在 node.data 中
    nodeData = context.node.data;
  } else if (context && context.data) {
    nodeData = context.data;
  } else if (context && typeof context === 'object' && context.value) {
    // 直接是节点数据
    nodeData = context;
  }

  // 检查是否是叶子节点且有script属性
  if (nodeData && nodeData.script && nodeData.value) {
    activeFunction.value = nodeData;
  }
};

const onFunctionDblClick = (context: any) => {
  // TDesign Tree 组件的数据结构
  let nodeData = null;

  // 检查是否有 node 属性（TDesign Tree 的标准结构）
  if (context && context.node) {
    // TDesign Tree 的节点数据通常在 node.data 中
    nodeData = context.node.data;
  } else if (context && context.data) {
    nodeData = context.data;
  } else if (context && typeof context === 'object' && context.value) {
    // 直接是节点数据
    nodeData = context;
  }

  // 检查是否是叶子节点且有script属性
  if (nodeData && nodeData.script && nodeData.value) {
    selectFunction(nodeData);
  }
};

const selectFunction = (func?: any) => {
  const selectedFunc = func || activeFunction.value;

  if (selectedFunc) {
    emits('dblclick', selectedFunc);
  }
};

// 节点标签点击事件（直接处理节点数据）
const onNodeLabelClick = (nodeData: any) => {
  if (nodeData && nodeData.script && nodeData.value) {
    activeFunction.value = nodeData;
  }
};

// 节点标签双击事件（直接处理节点数据）
const onNodeLabelDblClick = (nodeData: any, event?: Event) => {
  // 阻止事件冒泡
  if (event) {
    event.stopPropagation();
  }

  if (nodeData && nodeData.script && nodeData.value) {
    selectFunction(nodeData);
  }
};

// 获取函数分类（基于输出类型）
const getFunctionCategory = (functionValue: string) => {
  // 从当前函数数据中查找对应函数的输出类型
  const allFunctions = functionData.value.flatMap((category) => category.children || []);
  const targetFunction = allFunctions.find((func) => func.value === functionValue);

  if (targetFunction?.outputType) {
    // 使用后端提供的输出类型
    return VALUE_TYPE_MAP[targetFunction.outputType] || targetFunction.outputType;
  }

  // 如果没有找到，返回默认值
  return VALUE_TYPE_MAP['object'] || '字典';
};

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    MessagePlugin.success('代码已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
    MessagePlugin.error('复制失败');
  }
};

// 高亮搜索关键词
const highlightSearchKeyword = (text: string) => {
  if (!searchKeyword.value) {
    return text;
  }

  const keyword = searchKeyword.value.toLowerCase();
  const regex = new RegExp(`(${keyword})`, 'gi');
  return text.replace(
    regex,
    '<mark style="background: var(--td-warning-color-1); color: var(--td-warning-color-7); padding: 1px 2px; border-radius: 2px;">$1</mark>',
  );
};
</script>

<style lang="less" scoped>
.enhanced-function-list {
  height: 100%; /* 使用父容器的全部高度 */
  display: flex;
  flex-direction: column;

  .function-list-container {
    flex: 1;
    display: flex;
    min-height: 0;
    overflow: hidden;
  }

  .function-tree-panel {
    width: 320px;
    min-width: 280px;
    max-width: 400px;
    border-right: 1px solid var(--td-border-level-1-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;

    .panel-header {
      padding: 16px;
      border-bottom: 1px solid var(--td-border-level-1-color);
      background: var(--td-bg-color-container);

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .tree-container {
      flex: 1;
      padding: 8px;
      overflow-y: auto;
    }
  }

  .function-detail-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    overflow: hidden;
    padding: 16px;
  }
}

.tree-node-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 4px 0;

  span {
    flex: 1;
    font-size: 13px;
    line-height: 1.4;
  }

  .t-tag {
    margin-left: 8px;
    flex-shrink: 0;
  }
}

/* 树节点样式优化 */
:deep(.t-tree__item) {
  margin-bottom: 2px;
}

:deep(.t-tree__item--leaf) {
  .t-tree__label {
    padding: 6px 8px;
    border-radius: 4px;
    transition: all 0.2s;
  }

  &:hover .t-tree__label {
    background: var(--td-bg-color-container-hover);
  }

  &.t-tree__item--active .t-tree__label {
    background: var(--td-brand-color-1);
    color: var(--td-brand-color);
  }
}

:deep(.t-tree__item--branch) {
  .t-tree__label {
    font-weight: 500;
    color: var(--td-text-color-primary);
  }
}

.function-detail {
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  .function-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--td-border-level-1-color);
    flex-shrink: 0;

    .function-title-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 16px;

      .function-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--td-text-color-primary);
        flex: 1;
        min-width: 0;
        word-break: break-word;
      }

      .t-space {
        flex-shrink: 0;
      }
    }
  }

  .function-section {
    margin-bottom: 24px;
    flex-shrink: 0;

    h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--td-text-color-primary);
    }

    .code-preview-container {
      border-radius: 6px;
      overflow: hidden;
    }

    .function-description {
      background: var(--td-bg-color-container-select);
      border-radius: 6px;
      line-height: 1.5;
    }
  }
}

.examples-container {
  .example-item {
    margin-bottom: 16px;
    padding: 16px;
    background: var(--td-bg-color-container);
    border: 1px solid var(--td-border-level-1-color);
    border-radius: 8px;

    .example-header {
      margin-bottom: 8px;

      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: var(--td-text-color-primary);
      }
    }

    .example-code {
      margin-bottom: 8px;
      border-radius: 4px;
      overflow: hidden;
    }

    .example-result {
      display: flex;
      align-items: center;
      gap: 8px;

      .result-label {
        font-size: 12px;
        color: var(--td-text-color-secondary);
      }

      .result-code {
        background: var(--td-success-color-1);
        color: var(--td-success-color-7);
        padding: 4px 8px;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
      }
    }
  }
}

.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;

  .no-selection-content {
    text-align: center;
    color: var(--td-text-color-placeholder);
    max-width: 300px;

    .t-icon {
      margin-bottom: 16px;
      opacity: 0.6;
    }

    h3 {
      margin: 16px 0 8px 0;
      font-size: 18px;
      line-height: 1.4;
    }

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

/* Icon 样式优化 */
.t-button .t-icon {
  margin-right: 4px;
}

.t-button[size='small'] .t-icon {
  margin-right: 2px;
}
</style>
